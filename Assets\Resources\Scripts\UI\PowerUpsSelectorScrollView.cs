using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class PowerUpsSelectorScrollView : MonoBehaviour
{
    public ConfigsHandler configsHandler;

    public List<PowerUp> powerUps = new();
    Vector3 origin; // The origin of the UI
    public Button closeButton;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        gameObject.SetActive(false);
        origin = new(0f, -3f * transform.localScale.y, 5f); // Set the origin

        closeButton.onClick.AddListener(() =>
        {
            gameObject.SetActive(false);
        });

        powerUps = configsHandler.powerUps;
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OnEnable()
    {
        transform.position = origin;
        // Logs info from 1st power up
        if (powerUps.Count > 0)
        {
            Debug.Log("Name: " + powerUps[0].name);
            Debug.Log("Party Effects: " + powerUps[0].partyEffects.Count);
            //Print all party effects
            foreach (var effect in powerUps[0].partyEffects)
            {
                Debug.Log(effect.Key + ": " + effect.Value);
            }
            Debug.Log("Opponent Effects: " + powerUps[0].opponentEffects.Count);
            //Print all opponent effects
            foreach (var effect in powerUps[0].opponentEffects)
            {
                Debug.Log(effect.Key + ": " + effect.Value);
            }
            Debug.Log("Main Effect: " + powerUps[0].mainEffectType);
            Debug.Log("Full Main Effect: " + powerUps[0].fullMainEffect);
            Debug.Log("Main Effect Value: " + powerUps[0].mainEffectValue);
            Debug.Log("Bonus Effect: " + powerUps[0].bonusEffectType);
            Debug.Log("Bonus Effect Value: " + powerUps[0].bonusEffectValue);
        }
    }
}
