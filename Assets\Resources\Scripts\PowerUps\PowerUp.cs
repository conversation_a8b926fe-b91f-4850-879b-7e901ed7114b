using System.Collections.Generic;
using UnityEngine;

public class PowerUp
{
    public string id;         // Unique identifier
    public string itemId;   // Item id - id of the weapon has an item
    public string name;       // Display tag name
    public string description;      // Description
    public string partyDuringafullDano; // Party duringa full Dano
    public string partyDuringaNewDano; // Party duringa new Dano
    public string partyHealingAilment; // Party healing ailment
    public string partyRegeneratingPP; // Party regenerating PP
    public string partyRegeneratingHP; // Party regenerating HP
    public string partyBoostHP; // Party boost HP
    public string partyBoostATK; // Party boost ATK
    public string partyBoostDEF; // Party boost DEF
    public string partyBoostQI; // Party boost QI
    public string partyBoostLuck; // Party boost Luck
    public string partyBoostPR; // Party boost PR
    public string partyBoostSPD; // Party boost SPD
    public string opponentDuringafullDano; // Opponent duringa full Dano
    public string opponentDuringaNewDano; // Opponent duringa new Dano
    public string opponentNegativeHP; // Opponent negative HP
    public string opponentNegativeATK; // Opponent negative ATK
    public string opponentNegativeDEF; // Opponent negative DEF
    public string opponentNegativeQI; // Opponent negative QI
    public string opponentNegativeLuck; // Opponent negative Luck
    public string opponentNegativePR; // Opponent negative PR
    public string opponentNegativeSPD; // Opponent negative SPD
    public string opponentAGG; // Opponent AGG
    public string opponentStealth; // Opponent Stealth
    public string mainEffect; // Main effect
    public string secondaryEffect; // Secondary effect
    public int durationTurns; // Duration turns

    public Dictionary<string, string> partyEffects = new();
    public Dictionary<string, string> opponentEffects = new();
    public string mainEffectType;
    public string fullMainEffect;
    public float mainEffectValue;
    public string bonusEffectType;
    public float bonusEffectValue;



    public PowerUp(string id, string itemId, string name, string description, string partyDuringafullDano, string partyDuringaNewDano, string partyHealingAilment, string partyRegeneratingPP, string partyRegeneratingHP, string partyBoostHP, string partyBoostATK, string partyBoostDEF, string partyBoostQI, string partyBoostLuck, string partyBoostPR, string partyBoostSPD, string opponentDuringafullDano, string opponentDuringaNewDano, string opponentNegativeHP, string opponentNegativeATK, string opponentNegativeDEF, string opponentNegativeQI, string opponentNegativeLuck, string opponentNegativePR, string opponentNegativeSPD, string opponentAGG, string opponentStealth, string mainEffect, string secondaryEffect, int durationTurns)
    {
        this.id = id;
        this.itemId = itemId;
        this.name = name;
        this.description = description;
        this.partyDuringafullDano = partyDuringafullDano;
        this.partyDuringaNewDano = partyDuringaNewDano;
        this.partyHealingAilment = partyHealingAilment;
        this.partyRegeneratingPP = partyRegeneratingPP;
        this.partyRegeneratingHP = partyRegeneratingHP;
        this.partyBoostHP = partyBoostHP;
        this.partyBoostATK = partyBoostATK;
        this.partyBoostDEF = partyBoostDEF;
        this.partyBoostQI = partyBoostQI;
        this.partyBoostLuck = partyBoostLuck;
        this.partyBoostPR = partyBoostPR;
        this.partyBoostSPD = partyBoostSPD;
        this.opponentDuringafullDano = opponentDuringafullDano;
        this.opponentDuringaNewDano = opponentDuringaNewDano;
        this.opponentNegativeHP = opponentNegativeHP;
        this.opponentNegativeATK = opponentNegativeATK;
        this.opponentNegativeDEF = opponentNegativeDEF;
        this.opponentNegativeQI = opponentNegativeQI;
        this.opponentNegativeLuck = opponentNegativeLuck;
        this.opponentNegativePR = opponentNegativePR;
        this.opponentNegativeSPD = opponentNegativeSPD;
        this.opponentAGG = opponentAGG;
        this.opponentStealth = opponentStealth;
        this.mainEffect = mainEffect;
        this.secondaryEffect = secondaryEffect;
        this.durationTurns = durationTurns;
    }

    public void SetEffects()
    {
        if (!string.IsNullOrEmpty(partyDuringafullDano)) partyEffects.Add("partyDuringafullDano", partyDuringafullDano);
        if (!string.IsNullOrEmpty(partyDuringaNewDano)) partyEffects.Add("partyDuringaNewDano", partyDuringaNewDano);
        if (!string.IsNullOrEmpty(partyHealingAilment)) partyEffects.Add("partyHealingAilment", partyHealingAilment);
        if (!string.IsNullOrEmpty(partyRegeneratingPP)) partyEffects.Add("partyRegeneratingPP", partyRegeneratingPP);
        if (!string.IsNullOrEmpty(partyRegeneratingHP)) partyEffects.Add("partyRegeneratingHP", partyRegeneratingHP);
        if (!string.IsNullOrEmpty(partyBoostHP)) partyEffects.Add("partyBoostHP", partyBoostHP);
        if (!string.IsNullOrEmpty(partyBoostATK)) partyEffects.Add("partyBoostATK", partyBoostATK);
        if (!string.IsNullOrEmpty(partyBoostDEF)) partyEffects.Add("partyBoostDEF", partyBoostDEF);
        if (!string.IsNullOrEmpty(partyBoostQI)) partyEffects.Add("partyBoostQI", partyBoostQI);
        if (!string.IsNullOrEmpty(partyBoostLuck)) partyEffects.Add("partyBoostLuck", partyBoostLuck);
        if (!string.IsNullOrEmpty(partyBoostPR)) partyEffects.Add("partyBoostPR", partyBoostPR);
        if (!string.IsNullOrEmpty(partyBoostSPD)) partyEffects.Add("partyBoostSPD", partyBoostSPD);

        if (!string.IsNullOrEmpty(opponentDuringafullDano)) opponentEffects.Add("opponentDuringafullDano", opponentDuringafullDano);
        if (!string.IsNullOrEmpty(opponentDuringaNewDano)) opponentEffects.Add("opponentDuringaNewDano", opponentDuringaNewDano);
        if (!string.IsNullOrEmpty(opponentNegativeHP)) opponentEffects.Add("opponentNegativeHP", opponentNegativeHP);
        if (!string.IsNullOrEmpty(opponentNegativeATK)) opponentEffects.Add("opponentNegativeATK", opponentNegativeATK);
        if (!string.IsNullOrEmpty(opponentNegativeDEF)) opponentEffects.Add("opponentNegativeDEF", opponentNegativeDEF);
        if (!string.IsNullOrEmpty(opponentNegativeQI)) opponentEffects.Add("opponentNegativeQI", opponentNegativeQI);
        if (!string.IsNullOrEmpty(opponentNegativeLuck)) opponentEffects.Add("opponentNegativeLuck", opponentNegativeLuck);
        if (!string.IsNullOrEmpty(opponentNegativePR)) opponentEffects.Add("opponentNegativePR", opponentNegativePR);
        if (!string.IsNullOrEmpty(opponentNegativeSPD)) opponentEffects.Add("opponentNegativeSPD", opponentNegativeSPD);
        if (!string.IsNullOrEmpty(opponentAGG)) opponentEffects.Add("opponentAGG", opponentAGG);
        if (!string.IsNullOrEmpty(opponentStealth)) opponentEffects.Add("opponentStealth", opponentStealth);


        if (partyEffects.Count != 0) /// Se nao ha segundo positivo entao o bonus é um negativo no opponent se houver /// Nao ha dois positivos na party é letras(HEAL) e posivitos
        {
            foreach (var effect in partyEffects)
            {
                if (effect.Value == "HEAL" || effect.Value == "PP" || effect.Value == "HP" || effect.Value == "STEALTH")
                {
                    // Special effects always take priority as main effect
                    if (mainEffectValue < 100)
                    {
                        // Move current main to bonus if it exists
                        if (mainEffectValue > 0)
                        {
                            bonusEffectType = mainEffectType;
                            bonusEffectValue = mainEffectValue;
                        }
                        mainEffectType = effect.Key;
                        fullMainEffect = effect.Value;
                        mainEffectValue = 100;
                    }
                    else if (bonusEffectValue < 100)
                    {
                        // Set as bonus effect if main is already a special effect
                        bonusEffectType = effect.Key;
                        bonusEffectValue = 100;
                    }
                }
                else
                {
                    //Parse the values to float and get the highest and second highest
                    if (float.TryParse(effect.Value, out float value))
                    {
                        if (value > mainEffectValue)
                        {
                            // Move current main to bonus
                            if (mainEffectValue > 0)
                            {
                                bonusEffectType = mainEffectType;
                                bonusEffectValue = mainEffectValue;
                            }
                            mainEffectType = effect.Key;
                            mainEffectValue = value;
                        }
                        else if (value > bonusEffectValue && value != mainEffectValue)
                        {
                            // Set as second highest
                            bonusEffectType = effect.Key;
                            bonusEffectValue = value;
                        }
                    }
                }
            }
        }
        else
        {
            // Initialize with high values for opponent effects (we want lowest values)
            mainEffectValue = float.MaxValue;
            bonusEffectValue = float.MaxValue;

            foreach (var effect in opponentEffects)
            {
                if (effect.Value == "HEAL" || effect.Value == "PP" || effect.Value == "HP" || effect.Value == "STEALTH")
                {
                    // Special effects for opponents (rare case)
                    if (mainEffectValue == float.MaxValue)
                    {
                        mainEffectType = effect.Key;
                        fullMainEffect = effect.Value;
                        mainEffectValue = 100;
                    }
                    else if (bonusEffectValue == float.MaxValue)
                    {
                        bonusEffectType = effect.Key;
                        bonusEffectValue = 100;
                    }
                }
                else
                {
                    //Parse the values to float and get the lowest and second lowest
                    if (float.TryParse(effect.Value, out float value))
                    {
                        if (value < mainEffectValue)
                        {
                            // Move current main to bonus
                            if (mainEffectValue != float.MaxValue)
                            {
                                bonusEffectType = mainEffectType;
                                bonusEffectValue = mainEffectValue;
                            }
                            mainEffectType = effect.Key;
                            mainEffectValue = value;
                        }
                        else if (value < bonusEffectValue && value != mainEffectValue)
                        {
                            // Set as second lowest
                            bonusEffectType = effect.Key;
                            bonusEffectValue = value;
                        }
                    }
                }
            }

            // Reset to 0 if no effects were found
            if (mainEffectValue == float.MaxValue) mainEffectValue = 0;
            if (bonusEffectValue == float.MaxValue) bonusEffectValue = 0;
        }

    }
}
