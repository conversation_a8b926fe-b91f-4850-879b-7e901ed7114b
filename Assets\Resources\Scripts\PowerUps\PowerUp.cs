using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using UnityEngine;

public class PowerUp
{
    public string id;         // Unique identifier
    public string itemId;   // Item id - id of the weapon has an item
    public string name;       // Display tag name
    public string description;      // Description
    public string partyDuringafullDano; // Party duringa full Dano
    public string partyDuringaNewDano; // Party duringa new Dano
    public string partyHealingAilment; // Party healing ailment
    public string partyRegeneratingPP; // Party regenerating PP
    public string partyRegeneratingHP; // Party regenerating HP
    public string partyBoostHP; // Party boost HP
    public string partyBoostATK; // Party boost ATK
    public string partyBoostDEF; // Party boost DEF
    public string partyBoostQI; // Party boost QI
    public string partyBoostLuck; // Party boost Luck
    public string partyBoostPR; // Party boost PR
    public string partyBoostSPD; // Party boost SPD
    public string opponentDuringafullDano; // Opponent duringa full Dano
    public string opponentDuringaNewDano; // Opponent duringa new Dano
    public string opponentNegativeHP; // Opponent negative HP
    public string opponentNegativeATK; // Opponent negative ATK
    public string opponentNegativeDEF; // Opponent negative DEF
    public string opponentNegativeQI; // Opponent negative QI
    public string opponentNegativeLuck; // Opponent negative Luck
    public string opponentNegativePR; // Opponent negative PR
    public string opponentNegativeSPD; // Opponent negative SPD
    public string opponentAGG; // Opponent AGG
    public string opponentStealth; // Opponent Stealth
    public string mainEffect; // Main effect
    public string secondaryEffect; // Secondary effect
    public int durationTurns; // Duration turns

    public Dictionary<string, string> partyEffects = new();
    public Dictionary<string, string> opponentEffects = new();
    public string mainEffectType;
    public string fullMainEffect;
    public float mainEffectValue;
    public string bonusEffectType;
    public float bonusEffectValue;



    public PowerUp(string id, string itemId, string name, string description, string partyDuringafullDano, string partyDuringaNewDano, string partyHealingAilment, string partyRegeneratingPP, string partyRegeneratingHP, string partyBoostHP, string partyBoostATK, string partyBoostDEF, string partyBoostQI, string partyBoostLuck, string partyBoostPR, string partyBoostSPD, string opponentDuringafullDano, string opponentDuringaNewDano, string opponentNegativeHP, string opponentNegativeATK, string opponentNegativeDEF, string opponentNegativeQI, string opponentNegativeLuck, string opponentNegativePR, string opponentNegativeSPD, string opponentAGG, string opponentStealth, string mainEffect, string secondaryEffect, int durationTurns)
    {
        this.id = id;
        this.itemId = itemId;
        this.name = name;
        this.description = description;
        this.partyDuringafullDano = partyDuringafullDano;
        this.partyDuringaNewDano = partyDuringaNewDano;
        this.partyHealingAilment = partyHealingAilment;
        this.partyRegeneratingPP = partyRegeneratingPP;
        this.partyRegeneratingHP = partyRegeneratingHP;
        this.partyBoostHP = partyBoostHP;
        this.partyBoostATK = partyBoostATK;
        this.partyBoostDEF = partyBoostDEF;
        this.partyBoostQI = partyBoostQI;
        this.partyBoostLuck = partyBoostLuck;
        this.partyBoostPR = partyBoostPR;
        this.partyBoostSPD = partyBoostSPD;
        this.opponentDuringafullDano = opponentDuringafullDano;
        this.opponentDuringaNewDano = opponentDuringaNewDano;
        this.opponentNegativeHP = opponentNegativeHP;
        this.opponentNegativeATK = opponentNegativeATK;
        this.opponentNegativeDEF = opponentNegativeDEF;
        this.opponentNegativeQI = opponentNegativeQI;
        this.opponentNegativeLuck = opponentNegativeLuck;
        this.opponentNegativePR = opponentNegativePR;
        this.opponentNegativeSPD = opponentNegativeSPD;
        this.opponentAGG = opponentAGG;
        this.opponentStealth = opponentStealth;
        this.mainEffect = mainEffect;
        this.secondaryEffect = secondaryEffect;
        this.durationTurns = durationTurns;
    }

    public void SetEffects()
    {
        if (!string.IsNullOrEmpty(partyDuringafullDano)) partyEffects.Add("partyDuringafullDano", partyDuringafullDano);
        if (!string.IsNullOrEmpty(partyDuringaNewDano)) partyEffects.Add("partyDuringaNewDano", partyDuringaNewDano);
        if (!string.IsNullOrEmpty(partyHealingAilment)) partyEffects.Add("partyHealingAilment", partyHealingAilment);
        if (!string.IsNullOrEmpty(partyRegeneratingPP)) partyEffects.Add("partyRegeneratingPP", partyRegeneratingPP);
        if (!string.IsNullOrEmpty(partyRegeneratingHP)) partyEffects.Add("partyRegeneratingHP", partyRegeneratingHP);
        if (!string.IsNullOrEmpty(partyBoostHP)) partyEffects.Add("partyBoostHP", partyBoostHP);
        if (!string.IsNullOrEmpty(partyBoostATK)) partyEffects.Add("partyBoostATK", partyBoostATK);
        if (!string.IsNullOrEmpty(partyBoostDEF)) partyEffects.Add("partyBoostDEF", partyBoostDEF);
        if (!string.IsNullOrEmpty(partyBoostQI)) partyEffects.Add("partyBoostQI", partyBoostQI);
        if (!string.IsNullOrEmpty(partyBoostLuck)) partyEffects.Add("partyBoostLuck", partyBoostLuck);
        if (!string.IsNullOrEmpty(partyBoostPR)) partyEffects.Add("partyBoostPR", partyBoostPR);
        if (!string.IsNullOrEmpty(partyBoostSPD)) partyEffects.Add("partyBoostSPD", partyBoostSPD);

        if (!string.IsNullOrEmpty(opponentDuringafullDano)) opponentEffects.Add("opponentDuringafullDano", opponentDuringafullDano);
        if (!string.IsNullOrEmpty(opponentDuringaNewDano)) opponentEffects.Add("opponentDuringaNewDano", opponentDuringaNewDano);
        if (!string.IsNullOrEmpty(opponentNegativeHP)) opponentEffects.Add("opponentNegativeHP", opponentNegativeHP);
        if (!string.IsNullOrEmpty(opponentNegativeATK)) opponentEffects.Add("opponentNegativeATK", opponentNegativeATK);
        if (!string.IsNullOrEmpty(opponentNegativeDEF)) opponentEffects.Add("opponentNegativeDEF", opponentNegativeDEF);
        if (!string.IsNullOrEmpty(opponentNegativeQI)) opponentEffects.Add("opponentNegativeQI", opponentNegativeQI);
        if (!string.IsNullOrEmpty(opponentNegativeLuck)) opponentEffects.Add("opponentNegativeLuck", opponentNegativeLuck);
        if (!string.IsNullOrEmpty(opponentNegativePR)) opponentEffects.Add("opponentNegativePR", opponentNegativePR);
        if (!string.IsNullOrEmpty(opponentNegativeSPD)) opponentEffects.Add("opponentNegativeSPD", opponentNegativeSPD);
        if (!string.IsNullOrEmpty(opponentAGG)) opponentEffects.Add("opponentAGG", opponentAGG);
        if (!string.IsNullOrEmpty(opponentStealth)) opponentEffects.Add("opponentStealth", opponentStealth);

        Debug.Log("Starting SetEffects - Party count: " + partyEffects.Count + ", Opponent count: " + opponentEffects.Count);


        // Initialize values
        mainEffectType = null;
        fullMainEffect = null;
        mainEffectValue = 0;
        bonusEffectType = null;
        bonusEffectValue = 0;

        // Step 1: Check for special effects first (highest priority)
        foreach (var effect in partyEffects.Concat(opponentEffects))
        {
            if (effect.Value == "HEAL" || effect.Value == "PP" || effect.Value == "HP" || effect.Value == "STEALTH")
            {
                mainEffectType = effect.Key;
                fullMainEffect = effect.Value;
                mainEffectValue = 100;
                break; // Special effects take absolute priority
            }
        }

        // If no special effect found, proceed with numeric logic
        if (mainEffectType == null)
        {
            // Parse all numeric values
            var partyNumericEffects = new List<(string key, float value)>();
            var opponentNumericEffects = new List<(string key, float value)>();

            foreach (var effect in partyEffects)
            {
                Debug.Log("Trying to parse party effect: " + effect.Key + " = '" + effect.Value + "'");
                if (float.TryParse(effect.Value, NumberStyles.Float, CultureInfo.InvariantCulture, out float value) && value != 0)
                {
                    Debug.Log("Successfully parsed: " + effect.Key + " = " + value);
                    partyNumericEffects.Add((effect.Key, value));
                }
                else
                {
                    Debug.Log("Failed to parse or value is 0: " + effect.Key + " = '" + effect.Value + "'");
                }
            }

            foreach (var effect in opponentEffects)
            {
                Debug.Log("Trying to parse opponent effect: " + effect.Key + " = '" + effect.Value + "'");
                if (float.TryParse(effect.Value, out float value) && value != 0)
                {
                    Debug.Log("Successfully parsed: " + effect.Key + " = " + value);
                    opponentNumericEffects.Add((effect.Key, value));
                }
                else
                {
                    Debug.Log("Failed to parse or value is 0: " + effect.Key + " = '" + effect.Value + "'");
                }
            }

            // Debug: Show what was parsed
            Debug.Log("Party numeric effects count: " + partyNumericEffects.Count);
            foreach (var effect in partyNumericEffects)
            {
                Debug.Log("Party numeric: " + effect.key + " = " + effect.value);
            }

            Debug.Log("Opponent numeric effects count: " + opponentNumericEffects.Count);
            foreach (var effect in opponentNumericEffects)
            {
                Debug.Log("Opponent numeric: " + effect.key + " = " + effect.value);
            }

            // Get positive and negative values
            var positivePartyEffects = partyNumericEffects.Where(e => e.value > 0).OrderByDescending(e => e.value).ToList();
            var negativeOpponentEffects = opponentNumericEffects.Where(e => e.value < 0).OrderBy(e => e.value).ToList();

            // Debug: Show filtered results
            Debug.Log("Positive party effects count: " + positivePartyEffects.Count);
            foreach (var effect in positivePartyEffects)
            {
                Debug.Log("Positive party: " + effect.key + " = " + effect.value);
            }

            Debug.Log("Negative opponent effects count: " + negativeOpponentEffects.Count);
            foreach (var effect in negativeOpponentEffects)
            {
                Debug.Log("Negative opponent: " + effect.key + " = " + effect.value);
            }

            // Step 2: Determine main effect
            if (positivePartyEffects.Any())
            {
                // Highest positive value from party effects
                var mainEffect = positivePartyEffects.First();
                mainEffectType = mainEffect.key;
                mainEffectValue = mainEffect.value;
            }
            else if (negativeOpponentEffects.Any())
            {
                // Lowest (most negative) value from opponent effects
                var mainEffect = negativeOpponentEffects.First();
                mainEffectType = mainEffect.key;
                mainEffectValue = mainEffect.value;
            }
        }

        // Step 3: Determine bonus effect based on where main effect came from
        if (mainEffectType != null)
        {
            bool mainIsFromParty = partyEffects.ContainsKey(mainEffectType);
            bool mainIsSpecial = (mainEffectValue == 100);

            if (mainIsFromParty || (mainIsSpecial && partyEffects.ContainsKey(mainEffectType)))
            {
                // Case A: Main effect is from partyEffects

                // First try to find another positive value in partyEffects
                var otherPositiveEffects = new List<(string key, float value)>();
                foreach (var effect in partyEffects)
                {
                    if (effect.Key != mainEffectType && float.TryParse(effect.Value, out float value) && value > 0)
                    {
                        otherPositiveEffects.Add((effect.Key, value));
                    }
                }

                if (otherPositiveEffects.Any())
                {
                    // Use second highest positive value from party effects
                    var bonusEffect = otherPositiveEffects.OrderByDescending(e => e.value).First();
                    bonusEffectType = bonusEffect.key;
                    bonusEffectValue = bonusEffect.value;
                }
                else
                {
                    // Use lowest (most negative) value from opponent effects
                    var negativeOpponentEffects = new List<(string key, float value)>();
                    foreach (var effect in opponentEffects)
                    {
                        if (float.TryParse(effect.Value, out float value) && value < 0)
                        {
                            negativeOpponentEffects.Add((effect.Key, value));
                        }
                    }

                    if (negativeOpponentEffects.Any())
                    {
                        var bonusEffect = negativeOpponentEffects.OrderBy(e => e.value).First();
                        bonusEffectType = bonusEffect.key;
                        bonusEffectValue = bonusEffect.value;
                    }
                }
            }
            else
            {
                // Case B: Main effect is from opponentEffects

                // First try to find another negative value in opponentEffects
                var otherNegativeEffects = new List<(string key, float value)>();
                foreach (var effect in opponentEffects)
                {
                    if (effect.Key != mainEffectType && float.TryParse(effect.Value, out float value) && value < 0)
                    {
                        otherNegativeEffects.Add((effect.Key, value));
                    }
                }

                if (otherNegativeEffects.Any())
                {
                    // Use second lowest (second most negative) value from opponent effects
                    var bonusEffect = otherNegativeEffects.OrderBy(e => e.value).Skip(1).FirstOrDefault();
                    if (bonusEffect.key != null)
                    {
                        bonusEffectType = bonusEffect.key;
                        bonusEffectValue = bonusEffect.value;
                    }
                    else
                    {
                        // Only one other negative effect, use it
                        bonusEffect = otherNegativeEffects.First();
                        bonusEffectType = bonusEffect.key;
                        bonusEffectValue = bonusEffect.value;
                    }
                }
                else
                {
                    // Use highest positive value from party effects
                    var positivePartyEffects = new List<(string key, float value)>();
                    foreach (var effect in partyEffects)
                    {
                        if (float.TryParse(effect.Value, out float value) && value > 0)
                        {
                            positivePartyEffects.Add((effect.Key, value));
                        }
                    }

                    if (positivePartyEffects.Any())
                    {
                        var bonusEffect = positivePartyEffects.OrderByDescending(e => e.value).First();
                        bonusEffectType = bonusEffect.key;
                        bonusEffectValue = bonusEffect.value;
                    }
                }
            }
        }
        Debug.Log("Final main effect: " + mainEffectType + " = " + mainEffectValue);
        Debug.Log("Final bonus effect: " + bonusEffectType + " = " + bonusEffectValue);

    }
}
